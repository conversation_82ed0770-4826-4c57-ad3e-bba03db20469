package api

import (
	"deskcrm/components"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/mitchellh/mapstructure"

	jsoniter "github.com/json-iterator/go"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var (
	// defaultHeaders for the http request.
	DefaultHeaders = map[string]string{"referer": "deskcrm"}
)

// 判断httpcode, 框架层面没有处理
func ApiHttpCode(ctx *gin.Context, res *base.ApiResult) (err error) {
	if res.HttpCode != http.StatusOK {
		zlog.Warnf(ctx, "api http code not 200", res)
		err = components.ErrorHttpNotOk.Sprintf("HttpCode:：", res.HttpCode)
		return err
	}

	return
}

// 解析返回值
func DecodeResponse(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	var r struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}
	zlog.Errorf(ctx, "http response: %v", string(res.Response))
	zlog.Errorf(ctx, "http r: %v", r.Data)

	//检查错误，非0返回错误
	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(ctx, "http response code: %d", r.ErrNo)
		err = errors.New(r.ErrMsg)
		return
	}
	//解析Data
	if err = jsoniter.Unmarshal(r.Data, &output); err != nil {
		return
	}
	return errno, nil
}

// 解析php服务返回值，兼容空数组返回
func DecodePhpResponse(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	var r struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}
	//检查错误，非0返回错误
	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(ctx, "http response code: %d", r.ErrNo)
		err = errors.New(r.ErrMsg)
		return
	}
	// 处理空数组
	if string(r.Data) == "[]" {
		return errno, nil
	}
	//解析Data
	if err = jsoniter.Unmarshal(r.Data, &output); err != nil {
		return
	}
	return errno, nil
}

// 解析并返回rsp的data部分, 即通用rsp body结构
func DecodeRsp4Data(ctx *gin.Context, res *base.ApiResult) (rspData []byte, errno int, err error) {
	var r struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}
	//检查错误，非0返回错误
	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(ctx, "http response code: %d", r.ErrNo)
		err = errors.New(r.ErrMsg)
		return
	}
	// 返回Data
	return r.Data, errno, nil
}

// 解码 map data. 如果一个接口返回结果的 data 是个map 用这个函数. data 也可以是空数组. 兼容PHP
// 但是 data 对应的 struct 是兼容类型的
func decodeMapFuncCompatible2(ctx *gin.Context, data map[string]interface{}, output interface{}) error {
	config := mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           output,
	}
	decoder, err := mapstructure.NewDecoder(&config)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure newDecoder failed, %s", err)
		return err
	}

	err = decoder.Decode(data)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure decode failed, err:%s", err)
	}

	return err
}

func decodeSlice(ctx *gin.Context, input []interface{}, output interface{}) error {
	config := mapstructure.DecoderConfig{
		WeaklyTypedInput: true,
		Result:           output,
	}
	decoder, err := mapstructure.NewDecoder(&config)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure newDecoder failed, %s", err)
		return err
	}

	err = decoder.Decode(input)
	if err != nil {
		zlog.Errorf(ctx, "mapstructure decode failed, err:%s", err)
	}

	return err
}
func DecodeInterface(ctx *gin.Context, data interface{}, output interface{}) error {
	if data == nil {
		zlog.Infof(ctx, "data is null")
		return nil
	}

	switch data.(type) {
	// process data interface{}
	case []interface{}:
		sliceRet := data.([]interface{})
		// PHP 返回空对象也是个空的数组
		if len(sliceRet) == 0 {
			return nil
		} else {
			err := decodeSlice(ctx, sliceRet, output)
			return err
		}
	case map[string]interface{}:
		mapRet := data.(map[string]interface{})
		if err := decodeMapFuncCompatible2(ctx, mapRet, output); err != nil {
			return err
		}
	default:
		err := errors.New("can not support data type")
		zlog.Warnf(ctx, "can not support data type, (%T), %+v", data, data)
		return err
	}

	return nil
}

func GetRequestProxy(ctx *gin.Context, apiClient *base.ApiClient, path string, params map[string]interface{}) (*base.ApiResult, error) {
	return get(ctx, apiClient, path, params)
}

func PostRequestProxy(ctx *gin.Context, apiClient *base.ApiClient, path string, params map[string]interface{}) (*base.ApiResult, error) {
	return post(ctx, apiClient, path, params)
}

func get(ctx *gin.Context, apiClient *base.ApiClient, endpoint string, params map[string]interface{}) (*base.ApiResult, error) {
	opts := base.HttpRequestOptions{
		RequestBody: params,
	}
	return apiClient.HttpGet(ctx, endpoint, opts)
}

func post(ctx *gin.Context, apiClient *base.ApiClient, endpoint string, params map[string]interface{}) (*base.ApiResult, error) {
	opts := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      base.EncodeJson,
	}
	return apiClient.HttpPost(ctx, endpoint, opts)
}

// 辅导侧大部分是这种结构
type BzrResponse struct {
	ErrNo  int         `json:"errNo"`
	ErrStr string      `json:"errStr"`
	Data   interface{} `json:"data"`
}

func (b BzrResponse) GetErrNo() int {
	return b.ErrNo
}

func (b BzrResponse) GetErrStr() string {
	return b.ErrStr
}

func (b BzrResponse) GetData() interface{} {
	return b.Data
}

func AddCookie(ctx *gin.Context) {
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_e13a2d2419c79f6819022eeca4f6a2f01754639315",
	})
	ctx.Request = req
}
